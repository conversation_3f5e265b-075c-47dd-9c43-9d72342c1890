-- 创建上传任务表
CREATE TABLE IF NOT EXISTS upload_task (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键
    uid varchar(32) DEFAULT '', -- 用户UID
    team_id INT(8) DEFAULT 0, -- 团队ID
    name varchar(255) DEFAULT '', -- 文件名称
    local_path varchar(500) DEFAULT '', -- 本地文件路径
    url varchar(500) DEFAULT '', -- 云端URL
    hash varchar(64) DEFAULT '', -- 文件哈希值
    size BIGINT DEFAULT 0, -- 文件大小（字节）
    progress INT(3) DEFAULT 0, -- 上传进度（0-100）
    status INT(2) DEFAULT 0, -- 任务状态（0:等待 1:上传中 2:暂停 3:完成 4:失败 5:取消）
    type INT(2) DEFAULT 4, -- 文件类型（1:视频 2:音频 3:图片 4:其他）
    reason varchar(500) DEFAULT '', -- 失败原因
    folder_id varchar(40) DEFAULT '', -- 所属文件夹ID
    object_key varchar(500) DEFAULT '', -- OSS对象键
    object_id varchar(100) DEFAULT '', -- OSS对象ID
    upload_module varchar(50) DEFAULT 'oss', -- 上传模块
    checkpoint_data TEXT DEFAULT '', -- 断点续传数据
    created_at INT(13), -- 创建时间戳
    updated_at INT(13), -- 更新时间戳
    deleted_at INT(13) -- 删除时间戳
);

-- 创建上传任务表索引
CREATE INDEX IF NOT EXISTS idx_upload_task_uid ON upload_task(uid);
CREATE INDEX IF NOT EXISTS idx_upload_task_team_id ON upload_task(team_id);
CREATE INDEX IF NOT EXISTS idx_upload_task_status ON upload_task(status);
CREATE INDEX IF NOT EXISTS idx_upload_task_hash ON upload_task(hash); 