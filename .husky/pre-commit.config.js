/**
 * Pre-commit Hook 配置文件
 * 
 * 你可以通过修改这个文件来自定义 pre-commit 检查的行为
 */

module.exports = {
  // 是否启用 TypeScript 类型检查
  enableTypeCheck: true,
  
  // 是否启用 ESLint 检查
  enableLint: true,
  
  // 是否在检查失败时阻止提交（设为 false 则只显示警告）
  strict: true,
  
  // 要检查的包列表
  packages: {
    main: {
      enabled: true,
      typeCheckCommand: 'npm run typecheck',
      lintCommand: 'npm run lint'
    },
    renderer: {
      enabled: true,
      typeCheckCommand: 'npm run type-check',
      lintCommand: 'npm run lint'
    },
    preload: {
      enabled: true,
      typeCheckCommand: 'npm run typecheck',
      lintCommand: 'npm run lint'
    }
  },
  
  // 全局 lint 命令（在根目录执行）
  globalLintCommand: 'npm run lint',
  
  // 自定义消息
  messages: {
    start: '🔍 执行 pre-commit 检查...',
    typeCheckStart: '📝 检查 TypeScript 类型...',
    lintStart: '🔧 执行 ESLint 代码规范检查...',
    success: '✅ 所有检查通过，允许提交',
    failure: '❌ 检查失败，请修复问题后再次提交',
    skipHint: '如果你确定要跳过这些检查，可以使用: git commit --no-verify'
  }
}
