import { RenderableOverlay } from '@clipnest/remotion-shared/types'

/**
 * Props for the Main component
 */
export type MainProps = {
  /**
   * 需要渲染的 Overlay 数组
   */
  readonly overlays: RenderableOverlay[]

  /**
   * 播放器元数据. 用于内部 Layer 的渲染计算工作
   */
  playerMetadata?: {
    /** Duration in frames of the composition */
    readonly durationInFrames: number

    /** Frames per second of the composition */
    readonly fps: number

    /** Width of the composition */
    readonly width: number

    /** Height of the composition */
    readonly height: number
  }

  /** Base URL for media assets (optional) */
  readonly baseUrl?: string
}
