import { SubCacheManager } from '../types'
import opentype from 'opentype.js'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'

export class FontCacheManager extends SubCacheManager {

  // 内存缓存，用于同步访问 opentype.Font 对象
  private memoryCache: Map<string, opentype.Font> = new Map()

  // 字体元数据缓存，存储字体信息
  private fontMetadataCache: Map<string, { fontFamily: string; fontStyle: string }> = new Map()

  // 初始化状态标记
  private initialized = false

  /**
   * 初始化字体缓存 - 从主进程同步字体资源数据
   */
  private async initializeCache(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 从主进程获取所有字体资源
      const allEntries = await window.resource.getAllResources()
      const fontEntries = allEntries.filter(entry => entry.type === ResourceCacheType.FONT)

      console.log(`[FontCacheManager] 发现 ${fontEntries.length} 个字体资源`)

      this.initialized = true
    } catch (error) {
      console.error('[FontCacheManager] 字体缓存初始化失败:', error)
      throw error
    }
  }

  /**
   * 缓存字体文件并解析为 opentype.Font 对象
   * @param src 字体文件路径或URL
   * @param version 字体版本（可选）
   */
  public async cacheFont(src: string, version?: string): Promise<opentype.Font> {
    // 确保缓存已初始化
    await this.initializeCache()

    // 检查内存缓存
    const existed = this.memoryCache.get(src)
    if (existed) {
      return existed
    }

    // 处理远程字体文件 - 通过 IPC 获取本地缓存路径
    const cachedLocalUrl = await window.resource.fetchOrSaveResource({
      url: src,
      type: ResourceCacheType.FONT,
      version: version || '1.0'
    })

    if (!cachedLocalUrl) {
      throw new Error(`无法获取字体文件: ${src}`)
    }

    // 读取字体二进制数据
    const response = await fetch(`file://${cachedLocalUrl}`)
    const fontData: ArrayBuffer = await response.arrayBuffer()
    const font: opentype.Font = opentype.parse(fontData)

    // 缓存到内存
    this.memoryCache.set(src, font)

    // 缓存字体元数据
    const fontFamily = font.names.fontFamily?.zh || font.names.fontFamily?.en || '未知字体'
    const fontStyle = font.names.fontSubfamily?.zh || font.names.fontSubfamily?.en || 'Regular'
    this.fontMetadataCache.set(src, { fontFamily, fontStyle })

    console.debug(`[font.cache] 已将字体 ${fontFamily} 缓存到内存`)
    return font
  }

  /**
   * 同步获取字体对象（从内存缓存）
   */
  public getFont(src: string): opentype.Font | null {
    return this.memoryCache.get(src) || null
  }

  /**
   * 初始化方法：异步初始化字体缓存
   */
  override init(): void {
    void this.initializeCache().catch(error => {
      console.error('[FontCacheManager] 初始化失败:', error)
    })
  }

  /**
   * 清理过期的字体缓存 - 渲染进程只清理内存缓存
   */
  override async cleanup(): Promise<void> {
    // 渲染进程不需要执行清理操作，清理由主进程负责
    // 这里只是为了满足接口要求，实际清理逻辑在主进程中
    console.log('[FontCacheManager] 清理操作由主进程负责，跳过渲染进程清理')
  }
}
