// 编辑器状态数据结构
import { AspectRatio } from '@/modules/video-editor/shared'
import localforage from 'localforage'
import { ICacheManager, SubCacheManager } from '../types'
import { INDEXEDDB_DATABASE_NAME } from '@/constants/system'
import { PlayerDimensions, Track } from '@/modules/video-editor/types'
import { removeAllLocalUrlOfOverlay } from '@/modules/video-editor/utils/track-helper'
import { extractMessageFromIPCError } from '@/libs/cache/utils/error-handler'
import { PlayerMetadata } from '@app/shared/types/ipc/mixcut'

export interface EditorState {
  tracks: Track[]
  aspectRatio: AspectRatio
  playerMetadata: PlayerMetadata
  playerDimensions: PlayerDimensions
}

// 项目数据结构
export interface ProjectData {
  id: string
  editorState: EditorState
  timestamp: number
}

/**
 * 项目状态缓存管理器
 * 用于管理编辑器项目状态的自动保存和手动保存
 */
export class EditorStateCacheManager extends SubCacheManager {

  // 自动保存存储实例
  private readonly autoSaveStore: LocalForage

  // 云端加载请求去重缓存
  private readonly cloudLoadPromises: Map<string, Promise<EditorState | null>> = new Map()

  constructor(parent: ICacheManager) {
    super(parent)

    // 创建自动保存存储实例
    this.autoSaveStore = localforage.createInstance({
      name: INDEXEDDB_DATABASE_NAME,
      storeName: 'auto_saved_projects',
      description: '项目自动保存存储'
    })
  }

  /**
   * 保存编辑器状态到指定存储
   * @param projectId 项目ID
   * @param editorState 编辑器状态
   * @param manually 是否手动保存（true: 调用云端API，false: 保存到自动保存存储）
   */
  async saveProjectState(
    projectId: string,
    editorState: EditorState,
    manually = false
  ): Promise<void> {
    try {
      const projectData: ProjectData = {
        id: projectId,
        editorState,
        timestamp: Date.now(),
      }

      // 始终保存到自动保存存储
      await this.autoSaveStore.setItem(projectId, projectData)

      // 如果是手动保存，调用云端 API
      if (manually) {
        await this.saveToCloudAPI(projectId, editorState)
      }
    } catch (error) {
      console.error('保存编辑器状态失败:', error)
      throw new Error('保存编辑器状态失败')
    }
  }

  /**
   * 保存编辑器状态到云端 API
   * @param scriptId 项目ID（对应接口中的 script_id）
   * @param editorState 编辑器状态
   */
  private async saveToCloudAPI(scriptId: string, editorState: EditorState): Promise<void> {
    return window.editor.saveEditorState({
      scriptId,
      editorState: {
        ...editorState,
        tracks: removeAllLocalUrlOfOverlay(editorState.tracks)
      },
    })
  }

  /**
   * 从云端 API 加载编辑器状态（带去重机制）
   * @param scriptId 项目ID（对应接口中的 script_id）
   * @returns Promise<EditorState | null> 编辑器状态或 null（如果没有数据）
   */
  private async loadFromCloudAPI(scriptId: string): Promise<EditorState | null> {
    // 检查是否已有相同的请求正在进行
    const existingPromise = this.cloudLoadPromises.get(scriptId)
    if (existingPromise) {
      // console.log(`[EditorStateCacheManager] 复用现有的云端加载请求: projectId=${scriptId}`)
      return existingPromise
    }

    // 创建新的加载请求
    const loadPromise = this.performCloudLoad(scriptId)

    // 缓存请求 Promise
    this.cloudLoadPromises.set(scriptId, loadPromise)

    // 请求完成后清理缓存（无论成功还是失败）
    loadPromise.finally(() => {
      this.cloudLoadPromises.delete(scriptId)
    })

    return loadPromise
  }

  /**
   * 执行实际的云端加载操作
   * @param scriptId 项目ID
   * @returns Promise<EditorState | null>
   */
  private async performCloudLoad(scriptId: string): Promise<EditorState | null> {
    try {
      return await window.editor.loadEditorState({ scriptId })
    } catch (error) {
      throw new Error(`从云端加载编辑器状态失败: ${extractMessageFromIPCError(error)}`)
    }
  }

  /**
   * 从指定存储加载编辑器状态
   * @param scriptId 项目ID
   * @param manually 是否从手动保存加载（true: 从云端加载，false: 从自动保存存储加载）
   * @returns Promise that resolves with the editor state or null if not found
   */
  async loadProjectState(scriptId: string, manually = false): Promise<EditorState | null> {
    try {
      if (manually) {
        // 手动保存现在从云端加载
        return await this.loadFromCloudAPI(scriptId)
      } else {
        // 自动保存仍然从本地 IndexedDB 加载
        const projectData = await this.autoSaveStore.getItem<ProjectData>(scriptId)
        return projectData ? projectData.editorState : null
      }
    } catch (error) {
      console.error('加载编辑器状态失败:', error)

      // 如果是云端加载失败，需要特殊处理
      if (manually && error.message?.includes('从云端加载编辑器状态失败')) {
        // 抛出特殊错误，让上层处理强提醒和页面禁用
        throw new Error('CLOUD_LOAD_FAILED')
      }

      throw new Error('加载编辑器状态失败')
    }
  }

  /**
   * 清除项目的自动保存数据
   * @param scriptId 项目ID
   */
  async clearAutosave(scriptId: string): Promise<void> {
    try {
      await this.autoSaveStore.removeItem(scriptId)
    } catch (error) {
      console.error('清除自动保存失败:', error)
      throw new Error('清除自动保存失败')
    }
  }

  /**
   * 检查项目是否有自动保存数据
   * @param scriptId 项目ID
   * @returns Promise that resolves with the project data or null if not found
   */
  async hasAutosave(scriptId: string): Promise<ProjectData | null> {
    try {
      const projectData = await this.autoSaveStore.getItem<ProjectData>(scriptId)
      return projectData || null
    } catch (error) {
      console.error('检查自动保存失败:', error)
      throw new Error('检查自动保存失败')
    }
  }

  /**
   * 清理过期的项目数据
   */
  async cleanup(now: number, maxAge: number): Promise<void> {
    try {
      // 清理自动保存数据
      const autosaveKeysToRemove: string[] = []

      await this.autoSaveStore.iterate((value, key) => {
        if (now - value.timestamp > maxAge) {
          autosaveKeysToRemove.push(key)
        }
      })

      for (const key of autosaveKeysToRemove) {
        await this.autoSaveStore.removeItem(key)
      }

      // 不清理手动保存的项目数据，因为这些应该由用户主动删除
    } catch (error) {
      console.error('清理项目缓存失败:', error)
    }
  }
}
