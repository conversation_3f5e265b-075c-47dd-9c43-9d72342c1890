import React, { useEffect, useRef, useState } from 'react'
import { FieldErrors } from 'react-hook-form'
import { Play, Plus, RotateCcw, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { isEmpty } from 'lodash'
import { cn } from '@/components/lib/utils'
import { VideoSelectDrawer, VideoSelectDrawerRef } from '../../components/VideoSelectDrawer'
import { VideoPreviewPopover } from '../../components/VideoPreviewModal'

import { UploadModule } from '@app/shared/types/ipc/file-uploader'
import { useAuthStatus } from '@/components/auth/AuthGuard'
import { useUploadTasksContext } from '@/contexts/upload-task/context'
import { createFileFilters } from '@app/shared/file'
import { UploadTask } from '@app/shared/types/upload-task.types'

export interface SimpleVideo {
  url: string
  cover?: string
  customCover?: string
  orgCover?: string
  name?: string
}

interface VideoSelectionSectionProps {
  selectedVideos: SimpleVideo[]
  onVideoSelect: (videos: SimpleVideo[]) => void
  onRemoveVideo: (videoUrl: string) => void
  onUpdateVideoCover: (videoUrl: string, customCoverUrl: string, originalCover?: string) => void
  onReplaceVideo?: (oldVideoUrl: string, newVideo: SimpleVideo) => void
  errors?: FieldErrors
}

const CustomUploader: React.FC<{
  video: SimpleVideo,
  onComplete: (payload: UploadTask.CompleteEvent) => void
} > = ({
  video,
  onComplete
}) => {
  const { selectFiles, createTask, uploadFromPath, subscribe, unsubscribe } = useUploadTasksContext()
  const { authData, teamId } = useAuthStatus()

  const handleUploadFiles = async() => {
    const filePaths = await selectFiles({
      multiple: false,
      filters: createFileFilters(['IMAGE']),
    })

    if ( !filePaths || !authData?.accessToken || !teamId) {
      return
    }
    const taskIds: number[] = []

    try {
      const filePath = filePaths?.[0]
      const fileName = filePath.split(/[/\\]/).pop() || 'unknown'

      // 创建上传任务
      const task = await createTask({
        uid: authData.userId.toString(),
        team_id: teamId,
        name: fileName,
        local_path: filePath,
        upload_module: UploadModule.publishing
      })

      taskIds.push(task.id)
    } catch (error) {
      console.error('创建上传任务失败:', error)
    }

    try {
      await uploadFromPath({ taskIds })
    } catch (uploadError) {
      console.error('上传文件出错:', uploadError)
    }
  }

  useEffect(() => {
    const cb = (payload: any) => onComplete?.(payload)
    subscribe('batch-upload-complete', cb)
    return () => unsubscribe('batch-upload-complete', cb)
  }, [])

  return (
    <VideoCard className="relative overflow-hidden">
      <div className="w-full h-full overflow-hidden">
        {video.cover ? (
          <img
            src={video.cover}
            alt="视频封面"
            className="w-full h-full object-cover rounded-lg"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-xs text-neutral-400">
            无封面
          </div>
        )}
      </div>

      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
        <Button
          size="sm"
          variant="secondary"
          className="text-xs px-3 py-1 h-auto bg-neutral-600 hover:bg-neutral-700 text-white border-0"
          type="button"
          onClick={() => handleUploadFiles()}
        >
          点击上传
        </Button>
      </div>

      <div className="text-xs text-muted px-2 py-1 w-full bg-neutral-800/80">
        {video?.name ?? '暂无名称'}
      </div>
    </VideoCard>

  )
}

const VideoCard: React.FC<{
  className?: string
  children: React.ReactNode
  onClick?: () => void
}> = ({ children, className, onClick, ...rest }) => {
  return (
    <div
      {...rest}
      onClick={onClick}
      className={cn(className,
        'border-2 border-dashed border-neutral-700 h-84 aspect-[9/16] bg-neutral-800/50 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-500/50 hover:bg-neutral-800/90 transition-all duration-200')}
    >
      {children}
    </div>
  )
}

export const VideoSelectionSection: React.FC<VideoSelectionSectionProps> = ({
  selectedVideos,
  onVideoSelect,
  onRemoveVideo,
  onUpdateVideoCover,
  onReplaceVideo,
  errors
}) => {
  const videoSelectDrawerRef = useRef<VideoSelectDrawerRef>(null)
  const [replacingVideoUrl, setReplacingVideoUrl] = useState<string | null>(null)

  const handleOpenVideoSelect = () => {
    videoSelectDrawerRef.current?.open(selectedVideos)
  }

  const handleReplaceVideo = (currentVideo: SimpleVideo) => {
    setReplacingVideoUrl(currentVideo.url)
    videoSelectDrawerRef.current?.open([], true, selectedVideos)
  }

  const handleContinueSelectVideos = () => {
    videoSelectDrawerRef.current?.open(selectedVideos)
  }

  const handleVideoSelectResult = (videos: SimpleVideo[]) => {
    if (replacingVideoUrl && videos.length > 0) {
      const newVideo = videos[0]
      if (onReplaceVideo) {
        onReplaceVideo(replacingVideoUrl, newVideo)
      } else {
        const updatedVideos = selectedVideos.map(video =>
          video.url === replacingVideoUrl ? newVideo : video
        )
        onVideoSelect(updatedVideos)
      }
      setReplacingVideoUrl(null)
    } else {
      onVideoSelect(videos)
    }
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        添加视频 <span className="text-red-500">*</span>
      </Label>
      <p className="text-sm text-muted-foreground mb-4">
        视频时长≤15分钟，大小≤4GB，推荐上传720p以上分辨率，支持常见视频格式，推荐使用MP4。
      </p>

      {/* 视频选择区域 */}
      {isEmpty(selectedVideos) && (
        <div className="flex items-center gap-4 bg-neutral-700/50 w-fit p-5 rounded-2xl">
          <div className="flex gap-2">
            <VideoCard onClick={handleOpenVideoSelect}>
              <span className="text-neutral-600 dark:text-neutral-300 text-sm">添加视频</span>
            </VideoCard>

            <VideoCard>
              <span className="text-neutral-600 dark:text-neutral-300 text-sm">添加封面</span>
            </VideoCard>
          </div>
        </div>
      )}

      <div className="flex gap-2 flex-wrap">
        {/* 已选择的视频列表 */}
        {selectedVideos.map(video => (
          <div key={video.url} className="relative group/container flex items-center gap-4 bg-neutral-700/50 w-fit p-5 rounded-2xl">
            {/* 删除按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute -top-2 -right-2 z-20 h-6 w-6 p-0 bg-red-600 hover:bg-red-700 text-white rounded-full opacity-0 group-hover/container:opacity-100 transition-opacity duration-200"
              onClick={() => onRemoveVideo(video.url)}
            >
              <X className="w-3 h-3" />
            </Button>

            <div className="flex gap-2 items-center">
              {/* 第一个VideoCard - 视频预览卡片 */}
              <div className="relative group">
                <VideoCard className="relative overflow-hidden">
                  <div className="w-full h-full overflow-hidden">
                    {video.orgCover ? (
                      <img
                        src={video.orgCover}
                        alt="视频文件"
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-xs text-neutral-400">
                        无封面
                      </div>
                    )}
                  </div>

                  {/* 悬浮按钮层 */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                    <VideoPreviewPopover
                      video={video}
                      trigger={
                        <Button
                          size="sm"
                          variant="secondary"
                          className="text-xs px-3 py-1 h-auto bg-blue-600 hover:bg-blue-700 text-white border-0"
                        >
                          <Play className="w-3 h-3 mr-1" />
                          预览
                        </Button>
                      }
                    />
                    <Button
                      size="sm"
                      variant="secondary"
                      className="text-xs px-3 py-1 h-auto bg-neutral-600 hover:bg-neutral-700 text-white border-0"
                      onClick={() => handleReplaceVideo(video)}
                      type="button"

                    >
                      <RotateCcw className="w-3 h-3 mr-1" />
                      更换视频
                    </Button>
                  </div>

                  <div className="text-xs text-muted px-2 py-1 w-full bg-neutral-800/80">
                    {video?.name ?? '暂无名称'}
                  </div>
                </VideoCard>
              </div>

              {/* 第二个VideoCard - 封面卡片 */}
              <div className="relative group">
                <CustomUploader
                  video={video}
                  onComplete={(payload: UploadTask.CompleteEvent) => {
                    const newCover = payload.tasks?.[0].url
                    if (newCover) {
                      const originalCover = video.orgCover || video.cover
                      onUpdateVideoCover(video.url, newCover, originalCover)
                    }
                  }}
                />

              </div>
            </div>
          </div>
        ))}

        {/* 继续选择视频按钮 - 只在已有视频时显示 */}
        {!isEmpty(selectedVideos) && (
          <div className="flex items-center justify-center bg-neutral-700/30 border-2 border-dashed border-neutral-600 rounded-2xl p-5 min-h-[200px]">
            <Button
              type="button"
              variant="outline"
              onClick={handleContinueSelectVideos}
              className="flex flex-col items-center gap-2 h-auto py-4 px-6 border-neutral-500 hover:border-blue-500 hover:bg-blue-500/10"
            >
              <Plus className="w-6 h-6" />
              <span className="text-sm">继续选择视频</span>
            </Button>
          </div>
        )}
      </div>

      {errors?.videoList && (
        <p className="text-sm text-red-500">{(errors.videoList as any).message}</p>
      )}

      {/* 视频选择抽屉 */}
      <VideoSelectDrawer
        ref={videoSelectDrawerRef}
        onVideoSelect={handleVideoSelectResult}
      />
    </div>
  )
}
