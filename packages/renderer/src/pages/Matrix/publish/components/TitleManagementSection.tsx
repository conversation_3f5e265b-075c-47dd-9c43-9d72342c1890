import React from 'react'
import { Control, Controller, FieldErrors, useFieldArray } from 'react-hook-form'
import { Plus, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface TitleManagementSectionProps {
  control: Control<any>
  errors?: FieldErrors
}

export const TitleManagementSection: React.FC<TitleManagementSectionProps> = ({
  control,
  errors
}) => {
  const { fields: titleFields, append: appendTitle, remove: removeTitle } = useFieldArray({
    control,
    name: 'titles',
  })

  const MAX_TITLES = 100
  const canAddMore = titleFields.length < MAX_TITLES
  const formatTags = (value: string) => value.replace(/(#[^\s#]+)(?=#)/g, '$1 ')

  return (
    <div className="space-y-2">
      <Label className="flex items-center">标题</Label>
      <div className="flex gap-2 items-center">
        <Button
          type="button"
          variant="outline"
          onClick={() => appendTitle({ value: '' })}
          disabled={!canAddMore}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          添加标题
        </Button>
        <span className="text-sm text-muted-foreground">已选择 {titleFields.length}/{MAX_TITLES}</span>
      </div>
      <div className="text-muted text-sm">
        标题优先跟随视频本身携带的标题，若添加标题，则未添加标题的视频与添加的标题做随机组合。
      </div>
      <div className="space-y-3">
        {titleFields.map((field, index) => (
          <div key={field.id} className="flex items-center gap-2">
            <div className="flex-1">
              <Controller
                name={`titles.${index}.value`}
                control={control}
                render={({ field: { value, onChange } }) => {
                  return (
                    <Textarea
                      showLength
                      maxLength={200}
                      placeholder={`标题${index + 1}内容在此输入，可使用 #话题 标签`}
                      className={errors?.titles?.[index]?.value ? 'border-red-500' : ''}
                      value={value}
                      onChange={val => {
                        // console.log({ val })
                        onChange(formatTags(val.target.value))
                      }}
                    />
                  )
                }}
              />
              {errors?.titles?.[index]?.value && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.titles[index]?.value?.message}
                </p>
              )}
            </div>
            {titleFields.length > 1 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeTitle(index)}
                className="flex items-center gap-1 text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        ))}
      </div>

      {errors?.titles && (
        <p className="text-sm text-red-500">{(errors.titles as any).message}</p>
      )}
    </div>
  )
}
