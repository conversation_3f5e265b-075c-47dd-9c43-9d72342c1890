import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useSearchParams } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@radix-ui/react-separator'
import useRequest from '@/hooks/useRequest'
import { MatrixModule } from '@/libs/request/api/matrix'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { SimpleVideo, VideoSelectionSection } from './components/VideoSelectionSection'

import { TitleManagementSection } from './components/TitleManagementSection'
import { AccountSelectionSection } from './components/AccountSelectionSection'
import { PublishSettingsSection } from './components/PublishSettingsSection'
import { CartInfoSection } from './components/CartInfoSection'
import { ScheduledPublishSection } from './components/ScheduledPublishSection'
import { LoopPublishSection } from './components/LoopPublishSection'
import { queryClient } from '@/main'
import { toast } from 'react-toastify'
import { LoaderCircle } from 'lucide-react'
import {
  Account,
  AccountProduct,
  CreateMatrixParams,
  DetailDOS,
  DyPublishFormDetail,
  MountType,
  PublishMode,
  Setting,
  TimeType
} from '@/types/matrix/douyin'
import { LocationInfoSection } from './components/LocationInfoSelector'
import { PreviewPublishDialog, PreviewPublishDialogRef } from './components/PreviewPublishDialog'
import { PreviewMatrixParams, PreviewPublishProvider, usePreviewPublish } from './contexts/PreviewPublishContext'
import { buildDetailDOS } from './utils/buildDetailDOS'
import dayjs from 'dayjs'

const matrixPublishSchema = z.object({
  name: z.string().min(1, '计划名称不能为空').max(30, '计划名称不能超过30个字符'),
  marketingTarget: z.string().min(1, '营销目标不能为空'),
  videoList: z.array(z.object({
    cover: z.string().optional(),
    url: z.string()
  })).min(1, '请至少选择一个视频'),
  titles: z.array(z.object({
    value: z.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符')
  })).optional(),
  accountIds: z.array(z.number()).min(1, '请至少选择一个账号'),
  publishMode: z.number(),
  mountType: z.number(),
  setting: z.number(),
  timeType: z.number(),
  poi: z.object({
    poiId: z.string().optional(),
    poiName: z.string().optional()
  }).optional(),
  accountProducts: z.array(z.object({
    accountId: z.number(),
    products: z.array(z.object({
      title: z.string(),
      url: z.string()
    }))
  })).optional(),
  timeSetting: z.object({
    publishTime: z.number().optional(),
    period: z.number().optional(),
    periodType: z.array(z.number()).optional(),
    loopDays: z.array(z.number()).optional(),
    loopTime: z.string().optional(),
    numEachDay: z.number().optional()
  }).optional()
}).refine(data => {
  // 如果挂载模式为位置，则位置名称为必填
  if (data.mountType === MountType.LOCATION) {
    return data.poi && data.poi.poiName && data.poi.poiName.length > 0
  }
  return true
}, {
  message: '选择位置挂载时，位置名称为必填项',
  path: ['poi', 'poiName'],
}).refine(data => {
  // 如果挂载模式为购物车，则至少需要配置一个账号的商品
  if (data.mountType === MountType.CART) {
    return data.accountProducts && data.accountProducts.length > 0
  }
  return true
}, {
  message: '选择购物车挂载时，请至少为一个账号配置商品',
  path: ['accountProducts'],
}).refine(data => {
  // 账号数量不能大于视频数量
  return data.accountIds.length <= data.videoList.length
}, {
  message: '选择的账号数量不能大于视频数量',
  path: ['accountIds'],
}).refine(data => {
  // 如果发布时间类型为定时，则发布时间为必填
  if (data.timeType === TimeType.SCHEDULED) {
    return data.timeSetting && data.timeSetting.publishTime
  }
  return true
}, {
  message: '选择定时发布时，发布时间为必填项',
  path: ['timeSetting', 'publishTime'],
}).refine(data => {
  // 如果发布时间类型为定时，发布时间必须是两小时之后
  if (data.timeType === TimeType.SCHEDULED && data.timeSetting && data.timeSetting.publishTime) {
    const twoHoursLater = dayjs().add(2, 'hour')
    return dayjs(data.timeSetting.publishTime).isAfter(twoHoursLater)
  }
  return true
}, {
  message: '定时发布时间只能选两小时之后',
  path: ['timeSetting', 'publishTime'],
}).refine(data => {
  // 如果发布时间类型为循环，则循环配置为必填
  if (data.timeType === TimeType.LOOP) {
    return data.timeSetting &&
           data.timeSetting.loopDays &&
           data.timeSetting.loopDays.length > 0 &&
           data.timeSetting.loopTime &&
           data.timeSetting.loopTime.length > 0
  }
  return true
}, {
  message: '选择循环发布时，循环日期和时间为必填项',
  path: ['timeSetting', 'loopDays'],
})

type MatrixPublishFormData = z.infer<typeof matrixPublishSchema>

const VideoPublishMainContent = () => {
  const [searchParams] = useSearchParams()

  // 使用 useMemo 缓存路由参数，避免不必要的重新计算
  const { planId, draftId } = useMemo(() => ({
    planId: searchParams.get('planId'),
    draftId: searchParams.get('draftId')
  }), [searchParams])

  const previewDialogRef = useRef<PreviewPublishDialogRef>(null)
  const { setDetailDOS } = usePreviewPublish()

  const [selectedAccounts, setSelectedAccounts] = useState<Account[]>([])
  const [selectedVideos, setSelectedVideos] = useState<SimpleVideo[]>([])
  const [currentDraftId, setCurrentDraftId] = useState<number | null>(null)

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
    watch,
    reset
  } = useForm<MatrixPublishFormData>({
    resolver: zodResolver(matrixPublishSchema),
    defaultValues: {
      name: '',
      marketingTarget: '',
      videoList: [],
      titles: [{ value: '' }],
      accountIds: [],
      publishMode: PublishMode.CHECK_IN,
      mountType: MountType.NONE,
      setting: Setting.ONE_ACCOUNT_ONE_VIDEO,
      timeType: TimeType.IMMEDIATE,
      timeSetting: {
        publishTime: Date.now(),
        period: 0,
        periodType: [],
        loopDays: [Date.now()],
        loopTime: '08:00',
        numEachDay: 1
      }
    }
  })

  const timeType = watch('timeType')
  const mountType = watch('mountType')

  const { data: formData, isLoading: isFormDataLoading } = useQuery({
    queryKey: [QUERY_KEYS.MATRIX_DRAFT_DETAIL, QUERY_KEYS.MATRIX_PLAN_DETAIL, planId, draftId],
    queryFn: async () => {
      if (draftId) {
        const data = await MatrixModule.dyAccount.draftDetail(parseInt(draftId))
        setCurrentDraftId(data.id)
        return data
      } else if (planId) {
        return await MatrixModule.dyAccount.taskDetail(parseInt(planId))
      }
      return null
    },
    staleTime: 0,
    enabled: !!(draftId || planId),
  })

  const loadFormData = useCallback((formData: DyPublishFormDetail) => {
    try {
      // 回显表单数据
      setValue('name', formData.name || '')
      setValue('marketingTarget', formData.marketingTarget || '')
      setValue('publishMode', formData.publishMode || PublishMode.CHECK_IN)
      setValue('mountType', formData.mountType || MountType.NONE)
      setValue('setting', formData.setting || Setting.ONE_ACCOUNT_ONE_VIDEO)
      setValue('timeType', formData.timeType || TimeType.IMMEDIATE)

      // 回显位置信息
      if (formData.poi) {
        setValue('poi', {
          poiId: formData.poi.poiId || '',
          poiName: formData.poi.poiName || ''
        })
      }

      // 回显时间设置
      if (formData.timeSetting) {
        setValue('timeSetting', {
          publishTime: formData.timeSetting.publishTime || Date.now(),
          period: formData.timeSetting.period || 0,
          periodType: formData.timeSetting.periodType || [],
          loopDays: formData.timeSetting.loopDays || [Date.now()],
          loopTime: formData.timeSetting.loopTime?.slice(0, 5) || '08:00', // 去掉秒数
          numEachDay: formData.timeSetting.numEachDay || 1
        })
      }

      // 回显标题数据
      if (formData.titles && formData.titles.length > 0) {
        setValue('titles', formData.titles.map(title => ({ value: title })))
      }

      // 回显账号产品数据
      if (formData.accountProducts) {
        console.log('VideoPublishMain - 回显 accountProducts 数据:', formData.accountProducts)
        setValue('accountProducts', formData.accountProducts)
      }

      // 回显账号数据
      if (formData.accountList && formData.accountList.length > 0) {
        setSelectedAccounts(formData.accountList)
        setValue('accountIds', formData.accountList.map(account => account.id))
      }

      // 回显视频数据
      if (formData.videoList && formData.videoList.length > 0) {
        const simpleVideos: SimpleVideo[] = formData.videoList
          .filter(video => video.url) // 过滤掉没有 url 的视频
          .map((video, index) => ({
            url: video.url!,
            cover: video.cover || '',
            orgCover: video.orgCover || video.cover || '',
            name: video.name || `视频${index + 1}`
          }))

        setSelectedVideos(simpleVideos)
        setValue('videoList', formData.videoList.filter(video => video.url).map(video => ({
          url: video.url!,
          cover: video.cover || '',
          orgCover: video.orgCover || video.cover || ''
        })))
      }
    } catch (error) {
      console.error('回显表单数据失败:', error)
      toast('回显数据失败，请重试', { type: 'error' })
    }
  }, [setValue, setSelectedAccounts, setSelectedVideos])

  // 当数据加载完成后进行回显
  useEffect(() => {
    if (formData) {
      loadFormData(formData)
    }
  }, [formData, loadFormData])

  // 处理账号选择
  const handleAccountSelect = (accounts: Account[]) => {
    setSelectedAccounts(accounts)
    setValue('accountIds', accounts.map(account => account.id))
  }

  // 移除单个账号
  const handleRemoveAccount = (accountId: number) => {
    const updatedAccounts = selectedAccounts.filter(account => account.id !== accountId)
    handleAccountSelect(updatedAccounts)

    // 同时从账号商品配置中移除
    const currentAccountProducts = getValues('accountProducts') || []
    const updatedAccountProducts = currentAccountProducts.filter((ap: AccountProduct) => ap.accountId !== accountId)
    setValue('accountProducts', updatedAccountProducts)
  }

  // 处理视频选择
  const handleVideoSelect = (videos: SimpleVideo[]) => {
    console.log('handleVideoSelect', videos)
    setSelectedVideos(videos)
    setValue('videoList', videos.map(video => ({
      cover: video.cover || '',
      orgCover: video.orgCover || video.cover || '',
      url: video.url
    })))
  }

  const handleRemoveVideo = (videoUrl: string) => {
    const updatedVideos = selectedVideos.filter(video => video.url !== videoUrl)
    handleVideoSelect(updatedVideos)
  }

  // 处理视频封面更新
  const handleUpdateVideoCover = (videoUrl: string, customCoverUrl: string, originalCover?: string) => {
    const updatedVideos = selectedVideos.map(video =>
      video.url === videoUrl
        ? {
          ...video,
          cover: customCoverUrl,
          orgCover: originalCover || video.orgCover || video.cover
        }
        : video
    )
    handleVideoSelect(updatedVideos)
  }

  // 处理视频替换
  const handleReplaceVideo = (oldVideoUrl: string, newVideo: SimpleVideo) => {
    const updatedVideos = selectedVideos.map(video =>
      video.url === oldVideoUrl ? newVideo : video
    )
    handleVideoSelect(updatedVideos)
  }

  // 构建预览数据，包含完整的账号信息
  const buildPreviewData = (data: MatrixPublishFormData): PreviewMatrixParams => {
    const submitData = buildSubmitData(data)
    return {
      ...submitData,
      accounts: selectedAccounts
    }
  }

  // 处理预览功能
  const handlePreview = () => {
    const data = getValues()
    const previewData = buildPreviewData(data)
    console.log({ previewData })

    previewDialogRef.current?.open(previewData)
  }

  // 处理预览确认发布
  const handlePreviewConfirm = (selectedDetailDOS: DetailDOS[]) => {
    const data = getValues()
    const submitData = buildSubmitData(data)

    // 更新 detailDOS 为选中的数据，并确保只包含选中账号的 ID
    const selectedAccountIds = Array.from(new Set(selectedDetailDOS.map(d => parseInt(d.accountId))))
    const updatedSubmitData: CreateMatrixParams = {
      ...submitData,
      detailDOS: selectedDetailDOS,
      accountIds: selectedAccountIds, // 只包含选中的账号 ID
      totalAccount: selectedAccountIds.length,
      totalVideo: selectedDetailDOS.length
    }

    // 同步更新 context 中的数据
    setDetailDOS(selectedDetailDOS)

    submitPublishMutation.mutate(updatedSubmitData)
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DY_PUSH_PLAN_LIST] })
  }

  const { mutate: handleSaveToDraft, isPending: isSaving } = useRequest(
    () => {
      const data = getValues()
      const draftData = buildSubmitData(data)

      // 如果是编辑模式，携带草稿ID
      const submitData = currentDraftId
        ? { ...draftData, id: currentDraftId }
        : draftData

      return MatrixModule.dyAccount.saveDraft(submitData)
    },
    {
      actionName: currentDraftId ? '更新草稿' : '保存到草稿箱',
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATRIX_DRAFT_LIST] })
      }
    }
  )

  // 数据转换函数
  const buildSubmitData = (data: MatrixPublishFormData): CreateMatrixParams => {
    const titles = data.titles?.map(t => t.value) || []
    const detailDOS = buildDetailDOS(
      selectedAccounts,
      selectedVideos,
      titles,
      data.accountProducts || [],
      data.setting,
      data.poi?.poiId || '',
      data.poi?.poiName || ''
    )

    return {
      name: data.name,
      marketingTarget: data.marketingTarget,
      publishMode: data.publishMode,
      mountType: data.mountType,
      setting: data.setting,
      timeType: data.timeType,
      totalAccount: selectedAccounts.length,
      totalVideo: selectedVideos.length,
      titles,
      detailDOS,
      poi: {
        poiId: data.poi?.poiId || '',
        poiName: data.poi?.poiName || ''
      },
      accountIds: selectedAccounts.map(account => account.id),
      accountProducts: data.accountProducts || [],
      videoList: selectedVideos.map(video => ({
        cover: video.cover || '',
        orgCover: video.orgCover || video.cover || '',
        url: video.url,
        name: video.name
      })),
      timeSetting: {
        publishTime: data.timeSetting?.publishTime || Date.now(),
        loopDays: data.timeSetting?.loopDays || [Date.now()],
        loopPeriod: 0,
        loopTime: data.timeSetting?.loopTime ? `${data.timeSetting?.loopTime}:00` : '08:00:00',
        numEachDay: data.timeSetting?.numEachDay || 1,
        period: data.timeSetting?.period || 0,
        periodType: data.timeSetting?.periodType || [],
      }
    }
  }

  const submitPublishMutation = useRequest(MatrixModule.dyAccount.publish, {
    actionName: '发布视频',
    onSuccess: () => {
      reset()
      setSelectedAccounts([])
      setSelectedVideos([])
    },
    onError: error => {
      console.error('发布失败:', error)
    }
  })

  const onSubmit = (data: MatrixPublishFormData) => {
    const submitData = buildSubmitData(data)
    submitPublishMutation.mutate(submitData)
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DY_PUSH_PLAN_LIST] })
  }

  const onInvalid = (errors: any) => {
    console.log({ errors })

    const firstError = Object.values(errors)[0] as any
    const errorMessage = firstError?.message || '表单校验失败'

    toast(`表单校验失败: ${errorMessage}`, {
      type: 'warning'
    })
  }

  const nameValue = watch('name') || ''

  if (isFormDataLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <LoaderCircle className="animate-spin" />
      </div>
    )
  }

  return (
    <div className="h-full bg-background/50 rounded-lg overflow-hidden">
      <div className="h-full overflow-auto p-6">
        <form onSubmit={handleSubmit(onSubmit, onInvalid)} className="space-y-8">
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-semibold text-gradient-brand">基本信息</h2>
            </div>

            <div className="space-y-6">
              {/* 计划名称 */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  计划名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  showLength
                  maxLength={30}
                  placeholder="请输入计划名称"
                  value={nameValue}
                  {...register('name')}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              {/* 营销目标 */}
              <div className="space-y-2">
                <Label htmlFor="marketingTarget" className="flex items-center gap-2">
                  营销目标 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="marketingTarget"
                  placeholder="请输入营销目标"
                  {...register('marketingTarget')}
                  className={errors.marketingTarget ? 'border-red-500' : ''}
                />
                {errors.marketingTarget && (
                  <p className="text-sm text-red-500">{errors.marketingTarget.message}</p>
                )}
              </div>

              {/* 视频选择 */}
              <VideoSelectionSection
                selectedVideos={selectedVideos}
                onVideoSelect={handleVideoSelect}
                onRemoveVideo={handleRemoveVideo}
                onUpdateVideoCover={handleUpdateVideoCover}
                onReplaceVideo={handleReplaceVideo}
                errors={errors}
              />

              {/* 动态标题 */}
              <TitleManagementSection
                control={control}
                errors={errors}
              />

              {/* 账号 */}
              <AccountSelectionSection
                selectedAccounts={selectedAccounts}
                onAccountSelect={handleAccountSelect}
                onRemoveAccount={handleRemoveAccount}
              />

              {/* 发布设置 */}
              <PublishSettingsSection
                control={control}
                errors={errors}
                isCartDisabled={!selectedAccounts || selectedAccounts.length === 0}
              />

              {/* 位置信息 - 当挂载模式为位置时显示 */}
              {mountType === MountType.LOCATION && (
                <LocationInfoSection
                  control={control}
                  errors={errors}
                />
              )}

              {/* 购物车信息 - 当挂载模式为购物车时显示 */}
              {mountType === MountType.CART && (
                <CartInfoSection
                  control={control}
                  errors={errors}
                  selectedAccounts={selectedAccounts}
                />
              )}

              {/* 定时发布配置 - 当发布时间类型为定时时显示 */}
              {timeType === TimeType.SCHEDULED && (
                <ScheduledPublishSection
                  control={control}
                />
              )}

              {/* 循环定时发布配置 - 当发布时间类型为循环时显示 */}
              {timeType === TimeType.LOOP && (
                <LoopPublishSection
                  control={control}
                />
              )}
            </div>
          </div>

          <Separator />

          {/* 提交按钮 */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              onClick={() => handleSaveToDraft() }
              loading={isSaving}
            >
              {currentDraftId ? '更新草稿' : '保存草稿箱'}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handlePreview}
              disabled={selectedAccounts.length === 0 || selectedVideos.length === 0}
            >
              预览
            </Button>

            <Button
              type="submit"
              loading={submitPublishMutation.isPending}
              className="min-w-[120px]"
            >
              {submitPublishMutation.isPending ? '发布中...' : '立即发布'}
            </Button>
          </div>

          <PreviewPublishDialog
            ref={previewDialogRef}
            onConfirm={handlePreviewConfirm}
          />
        </form>
      </div>
    </div>

  )
}

export const VideoPublishMain = () => {
  return (
    <PreviewPublishProvider>
      <VideoPublishMainContent />
    </PreviewPublishProvider>
  )
}
