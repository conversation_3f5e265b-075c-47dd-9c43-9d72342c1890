import React from 'react'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { useLoadTileImageInfo } from '@/hooks/material/useLoadTileImageInfo'
import { TileRenderer } from '@/components/material/TileRenderer'

export const VideoPreviewFrame: React.FC<{ overlay?: VideoOverlay }> = ({ overlay }) => {
  if (!overlay?.originalMeta || !overlay.originalMeta.tileUrl) {
    return (
      <div className="w-full h-full bg-gray-500 flex items-center justify-center">
        <div className="text-white/60 text-sm">无预览</div>
      </div>
    )
  }

  const { tileUrl, height, width } = overlay.originalMeta
  const aspectRatio = width && height ? width / height : 1

  const tileInfo = useLoadTileImageInfo(tileUrl)

  return (
    <div className="w-full h-full bg-gray-500 flex items-center justify-center relative overflow-hidden rounded-sm">
      {tileInfo ? (
        <TileRenderer
          tileInfo={tileInfo}
          aspectRatio={aspectRatio}
          currentFrame={0} // 显示第一帧作为预览
        />
      ) : (
        <div className="text-white/60 text-sm">加载中...</div>
      )}
    </div>
  )
}
