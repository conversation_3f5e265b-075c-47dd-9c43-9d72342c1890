import React from 'react'
import { Combo } from '@app/shared/types/ipc/mixcut'
import { EditorState } from '@/libs/cache/parts/editor.cache'
import { Mixcut } from '@/types/mixcut'

import { RenderableOverlay } from '@clipnest/remotion-shared/types'
import { MultiSelection } from './useMultiSelection'

// 混剪素材规则
export type MixcutMaterialRule = {
  // 启用随机打乱. 开启后,将指定分镜打乱顺序进行排列组合
  enableShuffle: boolean

  // *分镜素材智能截取:
  // 开启后，系统将分镜里的素材,如分镜时长5s，放入10秒的素材，将从10s的素材中智能随机选取5秒;若不开启则默认从头截取5秒
  enableSmartTrim: boolean
}

export type MixcutPageTabs = 'generation' | 'saved'

export type DeduplicateOperations =
  | 'rotate'            // 随机旋转
  | 'scale'             // 随机缩放
  | 'offset'            // 随机偏移
  | 'caption-position'  // 随机字幕位置
  | 'video-start'       // 随机视频起始点
  | 'speed'             // 随机变速
  | 'trim-end'          // 随机去片尾
  | 'mask'              // 随机蒙版

// 视频去重规则
export type VideoDeduplicateRule = {
  // 启用视频去重
  enableDeduplicate: boolean
  operationStatus: Map<DeduplicateOperations, boolean>
}

// 重复率筛选规则
export type DuplicateRateFilter = {
  // 重复率最小值 (0-100)
  minRate: number
  // 重复率最大值 (0-100)
  maxRate: number
}

export type MixcutContextValues = {
  state: EditorState
  activeTab: MixcutPageTabs
  setActiveTab(v: MixcutPageTabs): void

  generation: MultiSelection<GeneratedMixcut> & {
    generateCount: number
    setGenerateCount: React.Dispatch<React.SetStateAction<number>>

    generatedMixcuts: GeneratedMixcut[]

    batchUploadState: {
      visible: boolean
      completed: number
      total: number
    }
    /**
     * 批量上传选中的混剪结果
     */
    uploadSelectedPreviews: () => Promise<void>

    /**
     * 生成所有可能的混剪组合
     */
    generateCombinations: () => void

    // 重复率筛选状态
    duplicateRateFilter: DuplicateRateFilter
    setDuplicateRateFilter: (filter: DuplicateRateFilter) => void
  }

  saved: MultiSelection<Mixcut.SavedMixcut> & {
  }

  playerOverlays: RenderableOverlay[]

  rules: {
    material: MixcutMaterialRule,
    deduplicate: VideoDeduplicateRule,
  }
}

export type GeneratedMixcut = {
  cover?: string
  videoCombo: Combo
  narrationSelections: number[]
}

export const MixcutContext = React.createContext<MixcutContextValues>(null as any)

export const useMixcutContext = () => {
  const context = React.useContext(MixcutContext)
  if (!context) {
    throw new Error('useMixcutContext must be used within a MixcutProvider')
  }
  return context
}
