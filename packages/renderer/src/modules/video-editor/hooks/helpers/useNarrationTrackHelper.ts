import { OverlayType, SoundOverlay, TextOverlay } from '@clipnest/remotion-shared/types'
import { AiModule } from '@/libs/request/api/ai'
import { useCallback } from 'react'
import { isTwoOverlaysOverlap } from '@/modules/video-editor/utils/overlay-helper'
import { IndexableTrack } from '@/modules/video-editor/types'
import { toast } from 'react-toastify'
import { recognizeAudioAsText, textToSpeech } from '@/modules/video-editor/utils/ai-helper'
import { DEFAULT_OVERLAY } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'

type NarrationTrackHelper = {
  /**
   * 为单个 TextOverlay 生成语音并添加对应的 SoundOverlay
   * 同时调整 TextOverlay 的时长以匹配音频时长
   */
  generateSpeechForTextOverlay(
    overlay: TextOverlay,
    currentTrack: IndexableTrack,
    params: Omit<AiModule.TextToSpeechRequestParams, 'text'>
  ): Promise<void>

  /**
   * 识别 SoundOverlay 中的音频内容并创建对应的 TextOverlay
   */
  recognizeSoundOverlayAsText(soundOverlay: SoundOverlay, currentTrack: IndexableTrack): Promise<void>
}

export const useNarrationTrackHelper = (): NarrationTrackHelper => {
  const { tracks, updateTracks } = useEditorContext()

  const { generateDefaultCaptionOverlay } = useOverlayHelper()

  /**
   * 为单个 TextOverlay 生成语音并添加对应的 SoundOverlay
   * 同时调整 TextOverlay 的时长以匹配音频时长
   */
  const generateSpeechForTextOverlay = useCallback(
    async (
      targetOverlay: TextOverlay,
      currentTrack: IndexableTrack,
      params: Omit<AiModule.TextToSpeechRequestParams, 'text'>
    ): Promise<void> => {
      // 检查文字内容
      if (!targetOverlay.content || targetOverlay.content.trim() === '') {
        toast.error('文字内容为空，无法生成语音')
        return
      }

      toast.info('正在生成语音，请稍候...')

      const [result, error] = await textToSpeech({
        ...params,
        text: targetOverlay.content
      })

      if (error) {
        console.error('生成语音失败:', error)
        const errorMessage = error instanceof Error ? error.message : '生成语音时发生未知错误'
        toast.error(`生成语音失败: ${errorMessage}`)
        return
      }

      const { audioUrl, localAudioUrl, durationInFrames } = result

      // 创建新的 SoundOverlay
      const newSoundOverlay: SoundOverlay = {
        ...DEFAULT_OVERLAY,
        id: generateNewOverlayId(tracks),
        type: OverlayType.SOUND,
        content: audioUrl,
        src: audioUrl,
        localSrc: localAudioUrl,
        durationInFrames,
        from: targetOverlay.from, // 与 TextOverlay 相同的起始帧
        storyboardIndex: targetOverlay.storyboardIndex, // 保持相同的分镜索引
        styles: {
          volume: 1,
        },
      }

      // 更新 TextOverlay 的时长以匹配音频时长
      const updatedTextOverlay: TextOverlay = {
        ...targetOverlay,
        durationInFrames
      }

      // 在同一个 setState 中更新轨道（添加 SoundOverlay 并更新 TextOverlay）
      updateTracks(prevTracks => {
        return prevTracks.map((track, trackIndex) => {
          if (trackIndex !== currentTrack.index) {
            return track
          }

          // 检测范围：在目标轨道的目标分镜中，查找所有已存在的 SoundOverlay
          const existingSoundOverlays = track.overlays.filter(overlay =>
            overlay.type === OverlayType.SOUND &&
            overlay.storyboardIndex === newSoundOverlay.storyboardIndex
          ) as SoundOverlay[]

          // 冲突检测：查找与新 SoundOverlay 时间重叠的已存在 SoundOverlay
          const overlappingSoundOverlays = existingSoundOverlays.filter(existingSound =>
            isTwoOverlaysOverlap(newSoundOverlay, existingSound)
          )

          return {
            ...track,
            overlays: track.overlays
              .map(overlay => {
                // 更新目标 TextOverlay
                if (overlay.id === updatedTextOverlay.id) {
                  return updatedTextOverlay
                }
                return overlay
              })
              // 冲突解决：删除所有与新 SoundOverlay 时间重叠的已存在 SoundOverlay
              .filter(overlay => {
                if (overlay.type === OverlayType.SOUND && overlay.storyboardIndex === newSoundOverlay.storyboardIndex) {
                  return !overlappingSoundOverlays.some(overlapping => overlapping.id === overlay.id)
                }
                return true
              })
              .concat(newSoundOverlay) // 添加新的 SoundOverlay
          }
        })
      })

      toast.success('语音生成成功！')
    },
    [tracks, updateTracks]
  )

  /**
   * 识别 SoundOverlay 中的音频内容并创建对应的 TextOverlay
   */
  const recognizeSoundOverlayAsText = useCallback(
    async (soundOverlay: SoundOverlay, currentTrack: IndexableTrack): Promise<void> => {
      if (!soundOverlay.src) {
        toast.error('音频文件路径为空，无法识别')
        return
      }

      toast.info('正在识别音频内容，请稍候...')

      const [result, error] = await recognizeAudioAsText(soundOverlay.src)

      if (error) {
        console.error('音频识别失败:', error)
        const errorMessage = error instanceof Error ? error.message : '音频识别时发生未知错误'
        toast.error(`音频识别失败: ${errorMessage}`)
        return
      }

      const { text } = result

      if (!text || text.trim() === '') {
        toast.warning('未识别到有效的文字内容')
        return
      }

      // 4. 创建新的 TextOverlay
      const newTextOverlay: TextOverlay = generateDefaultCaptionOverlay({
        ...DEFAULT_OVERLAY,
        content: text.trim(),
        durationInFrames: soundOverlay.durationInFrames,
        from: soundOverlay.from, // 与 SoundOverlay 相同的起始帧
        storyboardIndex: soundOverlay.storyboardIndex, // 保持相同的分镜索引
      })

      // 5. 在同一个 setState 中更新轨道（添加 TextOverlay）
      updateTracks(prevTracks => {
        return prevTracks.map((track, index) => {
          if (index === currentTrack.index) {
            return {
              ...track,
              overlays: [...track.overlays, newTextOverlay]
            }
          }
          return track
        })
      })

      toast.success('音频识别成功！')
    },
    [tracks, updateTracks, generateDefaultCaptionOverlay]
  )

  return {
    generateSpeechForTextOverlay,
    recognizeSoundOverlayAsText
  }
}
