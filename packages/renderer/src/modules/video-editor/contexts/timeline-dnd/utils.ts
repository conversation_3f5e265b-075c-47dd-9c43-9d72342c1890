import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { IndexableTrack, Track } from '../../types'
import { findOverlay, findOverlayStoryboard, findTrackByOverlay } from '@/modules/video-editor/utils/overlay-helper'
import {
  findStoryboardByFromFrame,
  isOverlayAcceptableByTrack,
  SingleOverlayUpdatePayload
} from '@/modules/video-editor/utils/track-helper'
import { AdjustCalculator } from './adjust-calculator'
import { DraggableState, OverlaysAdjustment } from './types'

/**
 * 网格对齐函数
 */
export function snapToGrid(value: number) {
  const GRID_SIZE = 1 // 假设帧级别对齐
  return Math.round(value / GRID_SIZE) * GRID_SIZE
}

/**
 * 构建 Overlay 更新数据
 */
export function buildOverlayUpdates(tracks: Track[], overlaysAdjust?: OverlaysAdjustment) {
  if (!overlaysAdjust) return []

  const updates: SingleOverlayUpdatePayload[] = []

  overlaysAdjust.forEach(({
    fromFrameShift = 0,
    durationShift = 0,
    targetStoryboardIndex
  }, itemId) => {
    const overlayToAdjust = findOverlay(tracks, itemId)
    if (overlayToAdjust) {
      updates.push({
        ...overlayToAdjust,
        from: overlayToAdjust.from + fromFrameShift,
        durationInFrames: overlayToAdjust.durationInFrames + durationShift,
        ...(overlayToAdjust.type !== OverlayType.STORYBOARD && targetStoryboardIndex !== undefined && {
          storyboardIndex: targetStoryboardIndex
        })
      })
    }
  })

  return updates
}

export function calculateDraggableStateForMoving(
  tracks: Track[],
  currentOverlay: Overlay,
  targetStartFrame: number,
  targetTrack: IndexableTrack
): DraggableState {
  if (!currentOverlay || !isOverlayAcceptableByTrack(currentOverlay, targetTrack)) {
    return {
      draggable: false,
    }
  }

  const originalTrack = findTrackByOverlay(tracks, currentOverlay.id) || null

  const targetStoryboard = targetTrack.isGlobalTrack
    ? null
    : findStoryboardByFromFrame(tracks, targetStartFrame)

  return new AdjustCalculator(tracks).calcAdjustForMoving(
    currentOverlay,
    findOverlayStoryboard(tracks, currentOverlay),
    originalTrack,
    targetStoryboard,
    targetTrack,
    targetStartFrame,
  )
}
