import { useMemo } from 'react'
import { FPS } from '../../constants'

import { Track } from '@/modules/video-editor/types'
import { flatOverlaysFromTracks } from '@/modules/video-editor/utils/track-helper'
import { OverlayType } from '@clipnest/remotion-shared/types'

export type CompositionDuration = {
  /**
   * 每秒帧数
   */
  fps: number

  /**
   * 以帧为单位的总时长
   */
  durationInFrames: number

  /**
   * 以秒为单位的总时长
   */
  durationInSeconds: number

  getDurationInSeconds(): number

  getDurationInFrames(): number
}

export const useCompositionDuration = (tracks: Track[]): CompositionDuration => {
  // Calculate the total duration in frames based on overlays
  const durationInFrames = useMemo(() => {
    if (!tracks.length) return 0

    return flatOverlaysFromTracks(tracks)
      .reduce((maxEnd, overlay) => {
        if (overlay.type === OverlayType.STORYBOARD) return maxEnd
        const storyboard = overlay[overlay.storyboardIndex ?? -1]
        let endFrame = overlay.from + overlay.durationInFrames
        if (storyboard) {
          endFrame = Math.min(endFrame, storyboard.from + storyboard.durationInFrames)
        }
        return Math.max(maxEnd, endFrame)
      }, 0)
  }, [tracks])

  // Utility functions for duration conversions
  const getDurationInSeconds = () => durationInFrames / FPS
  const getDurationInFrames = () => durationInFrames

  return {
    durationInFrames,
    durationInSeconds: durationInFrames / FPS,
    getDurationInSeconds,
    getDurationInFrames,
    fps: FPS,
  }
}
