import { useMemo } from 'react'
import { FPS } from '../../constants'

import { Track } from '@/modules/video-editor/types'
import { flatOverlaysFromTracks } from '@/modules/video-editor/utils/track-helper'

export type CompositionDuration = {
  /**
   * 每秒帧数
   */
  fps: number

  /**
   * 以帧为单位的总时长
   */
  durationInFrames: number

  /**
   * 以秒为单位的总时长
   */
  durationInSeconds: number

  getDurationInSeconds(): number

  getDurationInFrames(): number
}

export const useCompositionDuration = (tracks: Track[]): CompositionDuration => {
  // Calculate the total duration in frames based on overlays
  const durationInFrames = useMemo(() => {
    if (!tracks.length) return FPS // Default minimum duration (1 second)

    const maxEndFrame = flatOverlaysFromTracks(tracks)
      .reduce((maxEnd, overlay) => {
        const endFrame = overlay.from + overlay.durationInFrames
        return Math.max(maxEnd, endFrame)
      }, 0)

    // Just use the exact frame count or minimum duration
    return Math.max(maxEndFrame, FPS)
  }, [tracks])

  // Utility functions for duration conversions
  const getDurationInSeconds = () => durationInFrames / FPS
  const getDurationInFrames = () => durationInFrames

  return {
    durationInFrames,
    durationInSeconds: durationInFrames / FPS,
    getDurationInSeconds,
    getDurationInFrames,
    fps: FPS,
  }
}
