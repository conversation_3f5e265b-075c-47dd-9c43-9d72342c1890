import { CrudableIpcClient } from '../crudable-ipc-client.js'
import { ResourceTypes } from '../resource-cache.types.js'
import { UploadTask } from '../upload-task.types.js'
import { UploadModule } from './file-uploader.js'

export interface SelectFilesParams {
  multiple?: boolean
  filters?: Array<{ name: string, extensions: string[] }>
  folder?: boolean
  uploadModule?: string
}

export interface BaseFolderUploadParams {
  folderPath: string
  parentFolderUuid: string
  uid: string
  teamId?: number
  maxSize?: number
  uploadModule: UploadModule
  library: ResourceTypes
}

export interface TeamFolderUploadParams {
  directoryType: 'team'
  apiPrefix: string
}

export interface StorageFolderUploadParams {
  directoryType: 'storage'
  apiPrefix?: never
}

export type FolderUploadParams = BaseFolderUploadParams & {
  folderOptions: TeamFolderUploadParams  | StorageFolderUploadParams
}

export interface UploadTaskResponse {
  success: boolean
  tasks?: UploadTask.IUploadTask[]
  error?: string
}

export interface UploadTaskIPCClientUniqueUtilities {

  /**
   * 获取用户上传任务
   * @param data 参数对象
   * @returns 任务列表
   */
  getUserTasks(data: { uid: string, status?: UploadTask.Status, teamId?: number | null }): UploadTask.IUploadTask[]

  /**
   * 获取文件夹下的上传任务
   * @param data 参数对象
   * @returns 任务列表
   */
  getTasksByFolder(data: { folderId: string, uid: string, teamId?: number | null }): UploadTask.IUploadTask[]

  /**
   * 搜索上传任务
   * @param data 参数对象
   * @returns 任务列表
   */
  searchTasks(data: { keyword: string, uid: string, teamId?: number | null }): UploadTask.IUploadTask[]

  /**
   * 获取上传任务统计
   * @param data 参数对象
   * @returns 统计结果
   */
  getTaskStats(data: { uid: string, teamId?: number | null }): UploadTask.StatsResult

  /**
   * 开始上传任务
   * @param data 参数对象
   * @returns 是否成功开始
   */
  startUpload(data: { id: number }): boolean

  /**
   * 暂停上传任务
   * @param data 参数对象
   * @returns 是否成功暂停
   */
  pauseUpload(data: { id: number }): Promise<boolean>

  /**
   * 恢复上传任务
   * @param data 参数对象
   * @returns 是否成功恢复
   */
  resumeUpload(data: { id: number }): Promise<boolean>

  /**
   * 取消上传任务
   * @param data 参数对象
   * @returns 是否成功取消
   */
  cancelUpload(data: { id: number }): Promise<boolean>

  /**
   * 重试上传任务
   * @param data 参数对象
   * @returns 是否成功重试
   */
  retryUpload(data: { id: number }): boolean

  /**
   * 批量操作上传任务
   * @param data 批量操作参数
   * @returns 操作的任务数量
   */
  batchOperation(data: UploadTask.BatchParams): number

  /**
   * 获取上传队列配置
   * @returns 队列配置
   */
  getQueueConfig(): UploadTask.QueueConfig

  /**
   * 更新上传队列配置
   * @param config 新的配置
   * @returns 是否更新成功
   */
  updateQueueConfig(config: Partial<UploadTask.QueueConfig>): boolean

  /**
   * 获取当前上传队列状态
   * @returns 队列状态信息
   */
  getQueueStatus(): {
    active_count: number
    pending_count: number
    paused_count: number
    max_concurrent: number
  }

  /**
   * 清理已完成的任务
   * @param data 参数对象
   * @returns 清理的任务数量
   */
  cleanupCompleted(data: { uid: string, teamId?: number | null }): number

  /**
   * 选择文件
   * @param data 参数对象
   * @returns 选择的文件路径列表
   */
  selectFiles(data: SelectFilesParams): Promise<string[]>

  /**
   * 从本地路径上传文件
   * @param data 参数对象
   * @returns 上传结果
   */
  uploadFromPath(data: {
    taskIds: number[],
  }): Promise<UploadTaskResponse>

  uploadFolder(data: FolderUploadParams): Promise<UploadTaskResponse>

  getUploadingTasksByLibrary(data: { library: ResourceTypes }): UploadTask.IUploadTask[]

}

/**
 * 上传任务 IPC 客户端接口
 */
export interface UploadTaskIPCClient extends CrudableIpcClient<
  UploadTask.IUploadTask,
  UploadTask.CreateParams,
  UploadTask.QueryParams,
  UploadTask.UpdateParams,
  UploadTask.StatsResult
>, UploadTaskIPCClientUniqueUtilities {
}
