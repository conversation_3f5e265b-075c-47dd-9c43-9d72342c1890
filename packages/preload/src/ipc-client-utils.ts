import { ipc<PERSON>enderer } from 'electron'
import IPCClients from '@app/shared/types/ipc-clients.js'
import { CrudableIpcClient } from '@app/shared/types/crudable-ipc-client.js'

/**
 * 创建类型安全的IPC客户端
 * @param key 平台前缀
 * @returns 类型安全的IPC客户端对象
 */
export function createTypedIPCClient<Key extends keyof IPCClients, Client = IPCClients[Key]>(
  key: Key,
): Client {
  // 递归创建代理对象，支持嵌套属性
  function createProxy(path: string[] = []): any {
    return new Proxy({}, {
      get: (_target, prop: string) => {
        const newPath = [...path, prop]

        // 返回一个函数，同时这个函数也是一个代理对象，支持进一步的属性访问
        const fn = (...args: any[]) => {
          const channel = `${key as string}:${newPath.join('.')}`
          return ipcRenderer.invoke(channel, ...args)
        }

        // 让函数也可以作为对象，支持进一步的属性访问
        return new Proxy(fn, {
          get: (_target, nextProp: string) => {
            return createProxy(newPath)[nextProp]
          }
        })
      }
    })
  }

  const proxy = createProxy() as Client

  // TODO: wrap `exposeInMainWorld` here?

  return proxy
}

export function exposeCrudable(client: CrudableIpcClient<any, any, any, any, any>) {
  return {
    create: (v: any) => client.create(v),
    get: (v: any) => client.get(v),
    list: (v: any) => client.list(v),
    update: (v: any) => client.update(v),
    delete: (v: any) => client.delete(v),
    permanentlyDelete: (v: any) => client.permanentlyDelete(v),
    batchDelete: (v: any) => client.batchDelete(v),
    batchPermanentlyDelete: (v: any) => client.batchPermanentlyDelete(v),
  }
}
