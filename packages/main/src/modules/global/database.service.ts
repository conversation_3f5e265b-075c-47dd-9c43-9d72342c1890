import { Injectable, Logger } from '@nestjs/common'
import Database from 'better-sqlite3'
import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'

const isDev = process.env.NODE_ENV === 'development'

/**
 * 数据库错误类
 */
export class DatabaseError extends Error {

  constructor(message: string, public readonly cause?: any) {
    super(message, { cause })
    this.name = 'DatabaseError'
  }
}

/**
 * 数据库服务
 * 负责管理 SQLite 数据库连接和基本操作
 */
@Injectable()
export class NestDatabaseService {

  private readonly logger = new Logger(NestDatabaseService.name)

  /**
     * SQLite数据库实例
     */
  private _db: Database.Database | null = null

  /**
     * 数据库文件路径
     */
  private readonly _dbPath: string

  /**
   * 迁移文件目录
   */
  private readonly _migrationsDir: string

  /**
     * 构造函数
     * @param dbName 数据库名称
     */
  constructor(dbName: string) {
    if (!dbName) {
      throw new DatabaseError('数据库名称不能为空')
    }

    // 设置数据库文件路径（存储在应用数据目录）
    this._dbPath = path.join(app.getPath('userData'), dbName)

    // 设置迁移文件目录
    this._migrationsDir = isDev
      ? path.join(process.cwd(), 'migrations')
      : path.join(process.resourcesPath, 'migrations') // 生产时在 resources/migrations
  }

  /**
     * 获取数据库实例
     */
  get db(): Database.Database {
    if (!this._db) {
      throw new DatabaseError('数据库尚未初始化，请先调用 init() 方法')
    }
    return this._db
  }

  /**
   * 初始化数据库
   */
  async init(): Promise<void> {
    if (this._db) {
      this.logger.log('数据库已经初始化')
      return
    }

    try {
      // 确保数据库目录存在
      const dbDir = path.dirname(this._dbPath)
      await fs.promises.mkdir(dbDir, { recursive: true })

      // 创建数据库连接
      this._db = new Database(this._dbPath, {
        verbose: undefined,
      })

      // 启用外键约束
      this._db.pragma('foreign_keys = ON')

      // 执行迁移
      await this.runMigrations()

      this.logger.log(`数据库服务初始化成功: ${this._dbPath}`)
    }
    catch (error) {
      this.logger.error('数据库初始化失败:', error)
      throw new DatabaseError('数据库初始化失败', error)
    }
  }

  /**
   * 执行数据库迁移
   */
  private async runMigrations(): Promise<void> {
    if (!this._db) {
      throw new DatabaseError('数据库尚未初始化')
    }

    // 创建迁移表（如果不存在）
    this._db.exec(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        applied_at INTEGER NOT NULL
      )
    `)

    // 检查迁移目录是否存在
    try {
      await fs.promises.access(this._migrationsDir)
    } catch {
      this.logger.log(`迁移目录不存在，将创建: ${this._migrationsDir}`)
      await fs.promises.mkdir(this._migrationsDir, { recursive: true })
      return
    }

    // 获取已应用的迁移
    const appliedMigrations = this._db.prepare('SELECT name FROM migrations').all().map(row => (row as { name: string }).name)

    // 读取迁移文件
    const migrationFiles = await fs.promises.readdir(this._migrationsDir)

    // 按文件名排序（通常是数字前缀）
    migrationFiles.sort()

    // 执行未应用的迁移
    for (const file of migrationFiles) {
      if (file.endsWith('.sql') && !appliedMigrations.includes(file)) {
        await this.executeMigration(file)
      }
    }
  }

  /**
   * 执行单个迁移文件
   */
  private async executeMigration(filename: string): Promise<void> {
    if (!this._db) {
      throw new DatabaseError('数据库尚未初始化')
    }

    const filePath = path.join(this._migrationsDir, filename)

    try {
      // 读取迁移文件内容
      const sql = await fs.promises.readFile(filePath, 'utf-8')

      // 开始事务
      this._db.exec('BEGIN TRANSACTION')

      try {
        // 执行SQL
        this._db.exec(sql)

        // 记录迁移
        const stmt = this._db.prepare('INSERT INTO migrations (name, applied_at) VALUES (?, ?)')
        stmt.run(filename, Date.now())

        // 提交事务
        this._db.exec('COMMIT')

        this.logger.log(`迁移执行成功: ${filename}`)
      } catch (error) {
        // 回滚事务
        this._db.exec('ROLLBACK')
        throw error
      }
    } catch (error) {
      this.logger.error(`迁移执行失败: ${filename}`, error)
      throw new DatabaseError(`迁移执行失败: ${filename}`, error)
    }
  }

  /**
     * 关闭数据库连接
     */
  protected async onCleanup(): Promise<void> {
    if (this._db) {
      this._db.close()
      this._db = null
      this.logger.log('数据库连接已关闭')
    }
  }

  /**
     * 健康检查
     */
  protected async onHealthCheck(): Promise<{
    healthy: boolean
    status: string
    details?: Record<string, any>
  }> {
    try {
      if (!this._db) {
        return {
          healthy: false,
          status: '数据库未初始化',
        }
      }

      // 执行简单查询测试连接
      this._db.prepare('SELECT 1').get()

      return {
        healthy: true,
        status: '数据库连接正常',
        details: {
          path: this._dbPath,
        },
      }
    }
    catch (error) {
      return {
        healthy: false,
        status: '数据库连接异常',
        details: {
          error: error instanceof Error ? error.message : String(error),
        },
      }
    }
  }
}
