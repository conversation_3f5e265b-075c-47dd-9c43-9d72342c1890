#!/usr/bin/env node

/**
 * Pre-commit Hook 脚本
 * 在提交前执行 TypeScript 类型检查和 ESLint 代码规范检查
 */

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import path from 'path'
import process from 'process'

// 配置
const config = {
  enableTypeCheck: true,
  enableLint: true,
  strict: true,
  packages: {
    main: {
      enabled: true,
      typeCheckCommand: 'npm run typecheck',
      path: 'packages/main'
    },
    renderer: {
      enabled: true,
      typeCheckCommand: 'npm run type-check',
      path: 'packages/renderer'
    }
  },
  globalLintCommand: 'npm run lint'
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

function execCommand(command, cwd = process.cwd(), silent = false) {
  try {
    const result = execSync(command, { 
      cwd, 
      encoding: 'utf8',
      stdio: silent ? 'pipe' : 'inherit'
    })
    return { success: true, output: result }
  } catch (error) {
    return { success: false, error }
  }
}

async function runPreCommitChecks() {
  log('🔍 执行 pre-commit 检查...', colors.cyan)
  
  let errorCount = 0
  const errors = []

  // 1. TypeScript 类型检查
  if (config.enableTypeCheck) {
    for (const [packageName, packageConfig] of Object.entries(config.packages)) {
      if (!packageConfig.enabled) continue
      
      log(`\n📝 检查 ${packageName} TypeScript 类型...`, colors.blue)
      
      const packagePath = path.resolve(packageConfig.path)
      if (!existsSync(packagePath)) {
        log(`⚠️  包目录不存在: ${packagePath}`, colors.yellow)
        continue
      }
      
      const result = execCommand(packageConfig.typeCheckCommand, packagePath, true)
      
      if (result.success) {
        log(`✅ ${packageName} TypeScript 类型检查通过`, colors.green)
      } else {
        log(`❌ ${packageName} TypeScript 类型检查失败`, colors.red)
        log(`   运行 'cd ${packageConfig.path} && ${packageConfig.typeCheckCommand}' 查看详细错误`, colors.yellow)
        errorCount++
        errors.push(`${packageName} TypeScript 类型检查`)
      }
    }
  }

  // 2. ESLint 代码规范检查
  if (config.enableLint) {
    log('\n🔧 执行 ESLint 代码规范检查...', colors.blue)
    
    const result = execCommand(config.globalLintCommand, process.cwd(), true)
    
    if (result.success) {
      log('✅ ESLint 代码规范检查通过', colors.green)
    } else {
      log('❌ ESLint 代码规范检查失败', colors.red)
      log('   运行 \'npm run lint\' 查看详细错误', colors.yellow)
      log('   运行 \'npm run lint:fix\' 尝试自动修复部分问题', colors.yellow)
      errorCount++
      errors.push('ESLint 代码规范检查')
    }
  }

  // 输出结果
  log('\n==========================================', colors.cyan)
  
  if (errorCount === 0) {
    log('✅ 所有检查通过，允许提交', colors.green)
    process.exit(0)
  } else {
    log(`❌ 发现 ${errorCount} 个检查失败`, colors.red)
    log('\n失败的检查:', colors.yellow)
    errors.forEach(error => log(`  - ${error}`, colors.red))
    
    log('\n请修复以上问题后再次提交。', colors.yellow)
    log('\n常用修复命令:', colors.cyan)
    log('  npm run lint:fix           # 自动修复 ESLint 问题', colors.blue)
    log('  npm run pre-commit:fix     # 运行修复和检查', colors.blue)
    
    log('\n查看详细错误:', colors.cyan)
    log('  npm run typecheck          # 查看所有 TypeScript 错误', colors.blue)
    log('  npm run lint               # 查看所有 ESLint 错误', colors.blue)
    
    log('\n如果你确定要跳过这些检查，可以使用:', colors.cyan)
    log('  git commit --no-verify', colors.blue)
    log('==========================================', colors.cyan)
    
    if (config.strict) {
      process.exit(1)
    } else {
      log('\n⚠️  警告模式：检查失败但允许提交', colors.yellow)
      process.exit(0)
    }
  }
}

// 运行检查
runPreCommitChecks().catch(error => {
  log(`❌ Pre-commit 检查过程中发生错误: ${error.message}`, colors.red)
  process.exit(1)
})
