{"name": "clipnest-client", "version": "1.0.0-beta.10", "author": {"name": "gaolingtech"}, "private": true, "type": "module", "main": "packages/entry-point.mjs", "scripts": {"start": "chcp 65001 && node packages/dev-mode.js ", "package": "npm i && npm run build && rimraf dist && electron-builder build --config electron-builder.mjs", "upload": "node -r dotenv/config scripts/auto-upload.js", "release": "npm run package && npm run upload", "build": "npm run build -ws --if-present", "test": "npx playwright test ./tests/e2e.spec.ts", "typecheck": "npm run typecheck -ws --if-present", "lint": "npm run lint -ws --if-present", "lint:fix": "npm run lint:fix -ws --if-present", "postinstall": "npx electron-rebuild", "test:env": "node -r dotenv/config scripts/env-test.js", "prepare": "husky", "pre-commit": "node scripts/pre-commit.js", "pre-commit:fix": "npm run lint:fix && npm run pre-commit"}, "workspaces": ["packages/*"], "engines": {"node": ">=22.0.0"}, "dependencies": {"@app/main": "*", "ali-oss": "^6.23.0", "better-sqlite3": "^11.10.0", "dunder-proto": "^1.0.1", "jszip": "^3.10.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@npmcli/map-workspaces": "4.0.2", "@playwright/test": "1.52.0", "@stylistic/eslint-plugin": "^5.2.3", "@types/ali-oss": "^6.16.11", "@types/better-sqlite3": "^7.6.13", "@types/node": "22.15.29", "@types/uuid": "^10.0.0", "cos-nodejs-sdk-v5": "^2.15.4", "dotenv": "^17.2.1", "electron": "36.3.2", "electron-builder": "26.0.12", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unused-imports": "^4.1.4", "glob": "11.0.2", "husky": "^9.1.7", "playwright": "^1.52.0", "rimraf": "^6.0.1"}}